# Flutter and Dart specific rules
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.util.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.** { *; }
-keep class io.flutter.plugins.** { *; }
-dontwarn io.flutter.embedding.**

# GetX Framework - Critical for reactive state management
-keep class get.** { *; }
-keep class com.github.jonataslaw.** { *; }
-keepclassmembers class * extends get.GetxController {
    <fields>;
    <methods>;
}
-keepclassmembers class * extends get.GetxService {
    <fields>;
    <methods>;
}
-keep class * extends get.GetxController { *; }
-keep class * extends get.GetxService { *; }

# Keep all controller classes in your app
-keep class app.ivent.iventapp.** { *; }
-keep class * extends get.GetxController { *; }

# Preserve reactive variables and observables
-keepclassmembers class * {
    get.Rx* *;
    get.RxList* *;
    get.RxMap* *;
    get.RxSet* *;
}

# Keep classes with @JsonSerializable annotation
-keep @com.google.gson.annotations.SerializedName class * { *; }
-keep class * {
    @com.google.gson.annotations.SerializedName <fields>;
}

# Keep model classes (API generated and custom)
-keep class * extends java.lang.Object {
    <fields>;
    <methods>;
}

# Preserve enums
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Keep Parcelable implementations
-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# Dio HTTP client
-keep class dio.** { *; }
-keep class retrofit2.** { *; }

# Video Player
-keep class io.flutter.plugins.videoplayer.** { *; }

# Camera
-keep class io.flutter.plugins.camera.** { *; }

# Mapbox
-keep class com.mapbox.** { *; }
-dontwarn com.mapbox.**

# Sentry
-keep class io.sentry.** { *; }
-dontwarn io.sentry.**

# Cached Network Image
-keep class flutter_cached_network_image.** { *; }

# Photo Manager
-keep class com.fluttercandies.photo_manager.** { *; }

# Geolocator
-keep class com.baseflow.geolocator.** { *; }

# Permission Handler
-keep class com.baseflow.permissionhandler.** { *; }

# Shared Preferences
-keep class io.flutter.plugins.sharedpreferences.** { *; }

# URL Launcher
-keep class io.flutter.plugins.urllauncher.** { *; }

# Path Provider
-keep class io.flutter.plugins.pathprovider.** { *; }

# Package Info
-keep class io.flutter.plugins.packageinfo.** { *; }

# Connectivity Plus
-keep class dev.fluttercommunity.plus.connectivity.** { *; }

# Image Picker
-keep class io.flutter.plugins.imagepicker.** { *; }

# Flutter Contacts
-keep class co.sunnyapp.flutter_contact.** { *; }

# PDF View
-keep class io.github.ponnamkarthik.toast.** { *; }

# Share Plus
-keep class dev.fluttercommunity.plus.share.** { *; }

# Native Splash
-keep class io.flutter.plugins.flutter_native_splash.** { *; }

# General rules for reflection
-keepattributes Signature
-keepattributes *Annotation*
-keepattributes EnclosingMethod
-keepattributes InnerClasses

# Keep line numbers for debugging
-keepattributes SourceFile,LineNumberTable
-renamesourcefileattribute SourceFile

# Remove logging in release builds
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}
