#!/bin/bash

# Test Release Build Script for iVent App
# This script helps test release builds locally to identify issues before Play Store deployment

set -e

echo "🚀 Starting Release Build Test for iVent App"
echo "============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if Flutter is installed
if ! command -v flutter &> /dev/null; then
    print_error "Flutter is not installed or not in PATH"
    exit 1
fi

print_status "Flutter found: $(flutter --version | head -n 1)"

# Clean previous builds
echo ""
echo "🧹 Cleaning previous builds..."
flutter clean
flutter pub get

print_status "Dependencies updated"

# Check for common issues
echo ""
echo "🔍 Checking for common release build issues..."

# Check if proguard-rules.pro exists
if [ -f "android/app/proguard-rules.pro" ]; then
    print_status "ProGuard rules file exists"
else
    print_error "ProGuard rules file missing! This is likely the cause of your issue."
    echo "   Create android/app/proguard-rules.pro with proper GetX rules"
    exit 1
fi

# Check for debug-only imports
echo "   Checking for debug-only imports..."
if grep -r "import 'package:flutter/foundation.dart'" lib/ | grep -v test; then
    print_warning "Found foundation.dart imports - ensure kDebugMode checks are used properly"
fi

# Check for asset declarations
echo "   Checking asset declarations..."
if grep -q "assets/" pubspec.yaml; then
    print_status "Assets declared in pubspec.yaml"
else
    print_warning "No assets found in pubspec.yaml"
fi

# Build release APK
echo ""
echo "🔨 Building release APK..."
flutter build apk --release --verbose

if [ $? -eq 0 ]; then
    print_status "Release APK built successfully"
    
    # Get APK size
    APK_PATH="build/app/outputs/flutter-apk/app-release.apk"
    if [ -f "$APK_PATH" ]; then
        APK_SIZE=$(du -h "$APK_PATH" | cut -f1)
        print_status "APK size: $APK_SIZE"
        print_status "APK location: $APK_PATH"
    fi
else
    print_error "Release APK build failed"
    exit 1
fi

# Build release App Bundle (for Play Store)
echo ""
echo "📦 Building release App Bundle..."
flutter build appbundle --release --verbose

if [ $? -eq 0 ]; then
    print_status "Release App Bundle built successfully"
    
    # Get AAB size
    AAB_PATH="build/app/outputs/bundle/release/app-release.aab"
    if [ -f "$AAB_PATH" ]; then
        AAB_SIZE=$(du -h "$AAB_PATH" | cut -f1)
        print_status "App Bundle size: $AAB_SIZE"
        print_status "App Bundle location: $AAB_PATH"
    fi
else
    print_error "Release App Bundle build failed"
    exit 1
fi

echo ""
echo "🎉 Release builds completed successfully!"
echo ""
echo "📋 Next Steps:"
echo "1. Install the APK on a physical device: adb install $APK_PATH"
echo "2. Test the IaBottomPanel widget functionality"
echo "3. Check for any gray boxes or missing content"
echo "4. If issues persist, check the ProGuard rules and add more specific keep rules"
echo ""
echo "🔧 Debugging Tips:"
echo "- Use 'adb logcat' to see runtime logs"
echo "- Look for GetX controller initialization messages"
echo "- Check for asset loading errors"
echo "- Verify reactive state management is working"
