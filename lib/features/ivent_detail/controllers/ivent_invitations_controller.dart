import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/core/widgets/foundation/dialogs/ia_alert_dialog.dart';
import 'package:ivent_app/features/app_navigation/app_navigation_pages.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_detail_state_manager.dart';
import 'package:ivent_app/features/ivent_detail/ivent_detail_pages.dart';

class IventInvitationsController extends BaseController<IventDetailSharedState> {
  final _invitableUsers = Rxn<SearchInvitableUsersByIventIdReturn>();
  final _invitableGroups = Rxn<SearchInvitableUsersByIventIdReturn>();
  final _collabs = Rxn<SearchCollabsReturn>();
  final _selectedInvitableGroups = RxList<GroupListItem>([]);
  final _selectedInvitableUsers = RxList<UserListItem>([]);

  IventInvitationsController(
    AuthService authService,
    IventDetailSharedState state,
  ) : super(authService, state);

  SearchInvitableUsersByIventIdReturn? get invitableUsers => _invitableUsers.value;
  SearchInvitableUsersByIventIdReturn? get invitableGroups => _invitableGroups.value;
  SearchCollabsReturn? get collabs => _collabs.value;
  List<GroupListItem> get selectedInvitableGroups => _selectedInvitableGroups;
  List<UserListItem> get selectedInvitableUsers => _selectedInvitableUsers;

  set invitableUsers(SearchInvitableUsersByIventIdReturn? value) => _invitableUsers.value = value;
  set invitableGroups(SearchInvitableUsersByIventIdReturn? value) => _invitableGroups.value = value;
  set collabs(SearchCollabsReturn? value) => _collabs.value = value;
  set selectedInvitableGroups(List<GroupListItem> value) => _selectedInvitableGroups.assignAll(value);
  set selectedInvitableUsers(List<UserListItem> value) => _selectedInvitableUsers.assignAll(value);

  int get selectedFriendCount {
    final memberCountFromGroups = selectedInvitableGroups.fold<int>(
      0,
      (previousValue, element) => previousValue + element.memberCount,
    );
    return memberCountFromGroups + selectedInvitableUsers.length;
  }

  List<String> get selectedFriendNames {
    final memberNamesFromGroups = selectedInvitableGroups.fold<List<String>>(
      [],
      (previousValue, element) {
        previousValue.addAll(element.memberFirstnames);
        return previousValue;
      },
    );
    final usernamesFromUsers = selectedInvitableUsers.map((element) => element.username).toList();
    return usernamesFromUsers + memberNamesFromGroups;
  }

  List<String> get selectedInvitableGroupIds {
    return selectedInvitableGroups.map((element) => element.groupId).toList();
  }

  List<String> get selectedInvitableUserIds {
    return selectedInvitableUsers.map((element) => element.userId).toList();
  }

  List<String> get selectedInvitableUserFirstNames {
    return selectedInvitableUsers.map((element) => element.username).toList();
  }

  String get getInvitedSummmaryText {
    const endingText = 'katılım bilgilerini içeren bir bildirim gönderdik.';

    if (selectedFriendCount == 0) {
      return '';
    } else if (selectedFriendCount == 1) {
      return "${selectedFriendNames[0]}'a $endingText";
    } else if (selectedFriendCount == 2) {
      return "${selectedFriendNames[0]} ve ${selectedFriendNames[1]}'a $endingText";
    } else {
      return '${selectedFriendNames[0]}, ${selectedFriendNames[1]} ve ${selectedFriendCount - 2} diğer arkadaşına $endingText';
    }
  }

  Future<void> getInitiallyInvitableUsersPage() async {
    try {
      Get.toNamed(IventDetailPages.whomYouJoin, arguments: state.iventId);
      await _loadInvitableUsersAndGroups();
    } catch (e) {
      handleError(e);
    }
  }

  Future<void> getInviteMoreUsersPage() async {
    try {
      Get.toNamed(IventDetailPages.inviteMorePeople, arguments: state.iventId);
      await _loadInvitableUsersAndGroups();
    } catch (e) {
      handleError(e);
    }
  }

  void toggleInvitableGroup(GroupListItem group) {
    if (selectedInvitableGroupIds.contains(group.groupId)) {
      selectedInvitableGroups = selectedInvitableGroups.where((element) => element.groupId != group.groupId).toList();
    } else {
      selectedInvitableGroups.add(group);
    }
  }

  void toggleInvitableUser(UserListItem user) {
    if (selectedInvitableUserIds.contains(user.userId)) {
      selectedInvitableUsers = selectedInvitableUsers.where((element) => element.userId != user.userId).toList();
    } else {
      selectedInvitableUsers.add(user);
    }
  }

  Future<void> joinIvent() async {
    if (selectedFriendCount == 0) return;

    await runSafe(
      () async {
        await squadMembershipsApi.joinIventAndCreateSquadByIventId(
          state.iventId,
          JoinIventAndCreateSquadByIventIdDto(
            groupIds: selectedInvitableGroupIds,
            userIds: selectedInvitableUserIds,
          ),
        );

        _clearSelections();
        Get.back();
      },
      tag: 'joinIvent',
    );
  }

  Future<void> joinIventAndInviteFriends(BuildContext context) async {
    if (selectedFriendCount == 0) return;

    final userFirstNames = selectedInvitableUserFirstNames;
    final userCount = selectedInvitableUsers.length;

    await runSafe(
      () async {
        await squadMembershipsApi.joinIventAndCreateSquadByIventId(
          state.iventId,
          JoinIventAndCreateSquadByIventIdDto(
            groupIds: selectedInvitableGroupIds,
            userIds: selectedInvitableUserIds,
          ),
        );

        _clearSelections();
        Get.back();

        Future.microtask(
          () => showDialog(
            context: context,
            builder: (context) => IaAlertDialog.iventJoin(
              userFirstNames: userFirstNames,
              userCount: userCount,
              onHomePage: () => Get.offAllNamed(AppNavigationPages.appNavigation),
              onDone: () => Get.back(),
            ),
          ),
        );
      },
      tag: 'joinIventAndInviteFriends',
    );
  }

  Future<void> inviteFriends(BuildContext context) async {
    if (selectedFriendCount == 0) return;

    final selectedFriendFirstNames = selectedInvitableUserFirstNames;

    await runSafe(
      () async {
        await squadMembershipsApi.inviteFriendsByIventId(
          state.iventId,
          InviteFriendsByIventIdDto(
            groupIds: selectedInvitableGroupIds,
            userIds: selectedInvitableUserIds,
          ),
        );

        _clearSelections();
        Get.close(2);

        Future.microtask(
          () => showDialog(
            context: context,
            builder: (context) => IaAlertDialog.iventInvite(
              userFirstNames: selectedFriendFirstNames,
              userCount: selectedFriendFirstNames.length,
              onHomePage: () => Get.offAllNamed(AppNavigationPages.appNavigation),
              onDone: () => Get.back(),
            ),
          ),
        );
      },
      tag: 'inviteFriends',
    );
  }

  Future<void> _loadInvitableUsersAndGroups() async {
    await runSafe(
      () async {
        final futures = await Future.wait([
          authService.squadMembershipsApi.searchInvitableUsersByIventId(
            state.iventId,
            FriendListingTypeEnum.group,
          ),
          authService.squadMembershipsApi.searchInvitableUsersByIventId(
            state.iventId,
            FriendListingTypeEnum.user,
          ),
        ]);

        invitableGroups = futures[0];
        invitableUsers = futures[1];
      },
      tag: 'loadInvitableUsersAndGroups',
    );
  }

  void _clearSelections() {
    selectedInvitableGroups.clear();
    selectedInvitableUsers.clear();
  }
}
