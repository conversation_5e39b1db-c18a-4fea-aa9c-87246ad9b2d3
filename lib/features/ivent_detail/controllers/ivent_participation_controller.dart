import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/core/widgets/foundation/dialogs/ia_alert_dialog.dart';
import 'package:ivent_app/core/widgets/ia_search_results_builder.dart';
import 'package:ivent_app/features/app_navigation/app_navigation_pages.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_detail_state_manager.dart';

class IventParticipationController extends BaseControllerWithSearch<IventDetailSharedState> {
  final _groupsResult = Rxn<SearchInvitableUsersByIventIdReturn>();
  final _usersResult = Rxn<SearchInvitableUsersByIventIdReturn>();
  final _selectedGroups = RxList<GroupListItem>([]);
  final _selectedUsers = RxList<UserListItem>([]);

  IventParticipationController(AuthService authService, IventDetailSharedState state) : super(authService, state);

  SearchInvitableUsersByIventIdReturn? get groupsResult => _groupsResult.value;
  SearchInvitableUsersByIventIdReturn? get usersResult => _usersResult.value;
  List<GroupListItem> get selectedGroups => _selectedGroups.toList();
  List<UserListItem> get selectedUsers => _selectedUsers.toList();

  @override
  bool get isResultsEmpty => (groupsResult?.groups.isEmpty ?? true) || (usersResult?.friends.isEmpty ?? true);

  @override
  InitialSearchBehavior get initialSearchBehavior => InitialSearchBehavior.loaded;

  @override
  Future<void> onSearch([String? query]) async {
    _groupsResult.value = await squadMembershipsApi.searchInvitableUsersByIventId(
      state.iventId,
      FriendListingTypeEnum.group,
      q: query,
    );
    _usersResult.value = await squadMembershipsApi.searchInvitableUsersByIventId(
      state.iventId,
      FriendListingTypeEnum.user,
      q: query,
    );
  }

  bool get isEnabled => selectedFriendCount > 0;

  int get selectedFriendCount {
    final memberCountFromGroups = selectedGroups.fold<int>(
      0,
      (previousValue, element) => previousValue + element.memberCount,
    );
    return memberCountFromGroups + selectedUsers.length;
  }

  List<String> get selectedFriendNames {
    final memberNamesFromGroups = selectedGroups.fold<List<String>>(
      [],
      (previousValue, element) {
        previousValue.addAll(element.memberFirstnames);
        return previousValue;
      },
    );
    final usernamesFromUsers = selectedUsers.map((element) => element.username).toList();
    return usernamesFromUsers + memberNamesFromGroups;
  }

  List<String> get selectedGroupIds => selectedGroups.map((e) => e.groupId).toList();
  List<String> get selectedUserIds => selectedUsers.map((e) => e.userId).toList();
  List<String> get selectedUserFirstNames => selectedUsers.map((e) => e.username).toList();

  void toggleGroup(GroupListItem group) {
    if (selectedGroupIds.contains(group.groupId)) {
      _selectedGroups.value = selectedGroups.where((element) => element.groupId != group.groupId).toList();
    } else {
      _selectedGroups.add(group);
    }
  }

  void toggleInvitableUser(UserListItem user) {
    if (selectedUsers.contains(user.userId)) {
      _selectedUsers.value = selectedUsers.where((element) => element.userId != user.userId).toList();
    } else {
      _selectedUsers.add(user);
    }
  }

  Future<void> joinIvent() async {
    if (selectedFriendCount == 0) return;

    await runSafe(tag: 'joinIvent', () async {
      await squadMembershipsApi.joinIventAndCreateSquadByIventId(
        state.iventId,
        JoinIventAndCreateSquadByIventIdDto(
          groupIds: selectedGroupIds,
          userIds: selectedUserIds,
        ),
      );

      _clearSelections();
      Get.back();

      Get.dialog(
        IaAlertDialog.iventJoin(
          userFirstNames: selectedUserFirstNames,
          userCount: selectedUserFirstNames.length,
          onHomePage: () => Get.offAllNamed(AppNavigationPages.appNavigation),
          onDone: () => Get.back(),
        ),
      );
    });
  }

  Future<void> inviteFriends() async {
    if (selectedFriendCount == 0) return;
    await runSafe(tag: 'inviteFriends', () async {
      await squadMembershipsApi.inviteFriendsByIventId(
        state.iventId,
        InviteFriendsByIventIdDto(
          groupIds: selectedGroupIds,
          userIds: selectedUserIds,
        ),
      );

      _clearSelections();
      Get.close(2);

      Get.dialog(
        IaAlertDialog.iventInvite(
          userFirstNames: selectedUserFirstNames,
          userCount: selectedUserFirstNames.length,
          onHomePage: () => Get.offAllNamed(AppNavigationPages.appNavigation),
          onDone: () => Get.back(),
        ),
      );
    });
  }

  void _clearSelections() {
    _selectedGroups.clear();
    _selectedUsers.clear();
  }
}
