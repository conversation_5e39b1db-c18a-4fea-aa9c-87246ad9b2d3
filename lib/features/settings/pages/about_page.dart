import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/settings/constants/settings_constants.dart';
import 'package:ivent_app/features/settings/controllers/settings_controller.dart';
import 'package:ivent_app/features/settings/utils/settings_dialogs.dart';
import 'package:ivent_app/features/settings/widgets/about/app_info_section.dart';
import 'package:ivent_app/features/settings/widgets/about/copyright_section.dart';
import 'package:ivent_app/features/settings/widgets/ia_settings_list_tile.dart';

class AboutPage extends StatelessWidget {
  const AboutPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SettingsController>();

    return IaScaffold.noSearch(
      title: SettingsConstants.aboutTitle,
      body: ListView(
        padding: const EdgeInsets.all(AppDimensions.padding20),
        children: [
          IaSettingsListTile(
            icon: AppAssets.lock,
            title: SettingsConstants.privacyPolicyItem,
            onTap: controller.openPrivacyPolicy,
          ),
          const SizedBox(height: AppDimensions.padding16),
          IaSettingsListTile(
            icon: AppAssets.notebook,
            title: SettingsConstants.termsOfServiceItem,
            onTap: controller.openTermsOfService,
          ),
        ],
      ),
    );
  }
}
