import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/settings/constants/settings_constants.dart';
import 'package:ivent_app/features/settings/controllers/privacy_settings_controller.dart';
import 'package:ivent_app/features/settings/widgets/common/settings_info_box.dart';
import 'package:ivent_app/features/settings/widgets/privacy/blocked_users_section.dart';

class PrivacySettingsPage extends StatefulWidget {
  const PrivacySettingsPage({super.key});

  @override
  State<PrivacySettingsPage> createState() => _PrivacySettingsPageState();
}

class _PrivacySettingsPageState extends State<PrivacySettingsPage> {
  late final PrivacySettingsController controller;

  @override
  void initState() {
    super.initState();
    controller = Get.put(PrivacySettingsController());
  }

  @override
  Widget build(BuildContext context) {
    return IaScaffold.noSearch(
      title: SettingsConstants.privacySettingsTitle,
      showBackButton: true,
      body: Obx(() => controller.isLoading()
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: controller.refreshPrivacySettings,
              child: ListView(
                padding: const EdgeInsets.all(AppDimensions.padding20),
                children: [
                  BlockedUsersSection(controller: controller),
                ],
              ),
            )),
    );
  }
}
