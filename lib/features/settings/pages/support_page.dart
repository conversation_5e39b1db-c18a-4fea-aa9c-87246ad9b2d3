import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/settings/constants/settings_constants.dart';
import 'package:ivent_app/features/settings/controllers/settings_controller.dart';
import 'package:ivent_app/features/settings/widgets/common/settings_info_box.dart';
import 'package:ivent_app/features/settings/widgets/ia_settings_list_tile.dart';
import 'package:ivent_app/features/settings/widgets/support/faq_section.dart';

class SupportPage extends StatelessWidget {
  const SupportPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SettingsController>();

    return IaScaffold.noSearch(
      title: SettingsConstants.supportTitle,
      body: ListView(
        padding: const EdgeInsets.all(AppDimensions.padding20),
        children: [
          IaSettingsListTile(
            icon: AppAssets.headphones,
            title: SettingsConstants.contactItem,
            onTap: controller.openWebsite,
          ),
        ],
      ),
    );
  }
}
