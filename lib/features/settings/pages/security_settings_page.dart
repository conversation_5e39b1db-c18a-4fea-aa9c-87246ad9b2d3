import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/settings/constants/settings_constants.dart';
import 'package:ivent_app/features/settings/controllers/security_settings_controller.dart';
import 'package:ivent_app/features/settings/widgets/ia_settings_list_tile.dart';
import 'package:ivent_app/features/settings/widgets/security/account_info_section.dart';
import 'package:ivent_app/features/settings/widgets/security/notification_settings_section.dart';
import 'package:ivent_app/features/settings/widgets/security/security_settings_section.dart';

class SecuritySettingsPage extends StatefulWidget {
  const SecuritySettingsPage({super.key});

  @override
  State<SecuritySettingsPage> createState() => _SecuritySettingsPageState();
}

class _SecuritySettingsPageState extends State<SecuritySettingsPage> {
  late final SecuritySettingsController controller;

  @override
  void initState() {
    super.initState();
    controller = Get.put(SecuritySettingsController());
  }

  @override
  Widget build(BuildContext context) {
    return IaScaffold.noSearch(
      title: SettingsConstants.securitySettingsTitle,
      showBackButton: true,
      body: Obx(() => controller.isLoading()
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: controller.refreshSettings,
              child: ListView(
                padding: const EdgeInsets.all(AppDimensions.padding16),
                children: [
                  AccountInfoSection(controller: controller),
                  const SizedBox(height: AppDimensions.padding20),
                  Obx(() => IaSettingsListTile(
                        icon: AppAssets.userSquare,
                        title: SettingsConstants.educationStatusItem,
                        titleColor: controller.getEducationStatusColor(),
                        trailing: DropdownButton<String>(
                          value: controller.educationStatus.value,
                          underline: const SizedBox(),
                          items: SettingsConstants.educationStatusMap.entries
                              .map((entry) => DropdownMenuItem(
                                    value: entry.key,
                                    child: Text(entry.value),
                                  ))
                              .toList(),
                          onChanged: (value) {
                            if (value != null) {
                              controller.updateEducationStatus(value);
                            }
                          },
                        ),
                      )),
                  const SizedBox(height: AppDimensions.padding20),
                  NotificationSettingsSection(controller: controller),
                  const SizedBox(height: AppDimensions.padding20),
                  SecuritySettingsSection(controller: controller),
                ],
              ),
            )),
    );
  }
}
