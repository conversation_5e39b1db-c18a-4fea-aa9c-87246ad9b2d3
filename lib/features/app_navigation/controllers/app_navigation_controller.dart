import 'package:get/get.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/core/controllers/shared_state.dart';
import 'package:ivent_app/core/services/auth_service.dart';

class AppNavigationController extends BaseController<FeatureSharedState> {
  AppNavigationController(AuthService authService, FeatureSharedState state) : super(authService, state);

  final _currentIndex = 0.obs;
  final _showBottomNav = true.obs;
  final _enableDrawer = false.obs;

  int get currentIndex => _currentIndex.value;
  bool get showBottomNav => _showBottomNav.value;
  bool get enableDrawer => _enableDrawer.value;
}
