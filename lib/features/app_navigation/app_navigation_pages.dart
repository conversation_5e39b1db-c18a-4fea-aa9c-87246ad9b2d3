import 'package:get/get.dart';
import 'package:ivent_app/core/middlewares/auth_middleware.dart';
import 'package:ivent_app/features/app_navigation/app_navigation_bindings.dart';
import 'package:ivent_app/features/app_navigation/pages/app_navigation_screen.dart';

class AppNavigationPages {
  AppNavigationPages._();

  static const appNavigation = '/appNavigation';

  static final routes = [
    GetPage(
      name: appNavigation,
      page: () => const AppNavigationScreen(),
      binding: AppNavigationBindings(),
      middlewares: [AuthMiddleware()],
    ),
  ];
}
