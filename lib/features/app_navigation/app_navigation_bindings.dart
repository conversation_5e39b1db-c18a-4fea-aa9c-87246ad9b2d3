 
import 'package:get/get.dart';
import 'package:ivent_app/core/controllers/shared_state.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/app_navigation/controllers/app_navigation_controller.dart';
import 'package:ivent_app/features/home/<USER>'; 
import 'package:ivent_app/features/notifications/controllers/notification_controller.dart';
import 'package:ivent_app/features/profile/profile_bindings.dart'; 
import 'package:ivent_app/features/side_menu/controllers/profile_side_menu_controller.dart';
import 'package:ivent_app/features/vibes/vibes_bindings.dart'; 

class AppNavigationBindings implements Bindings {
  @override
  void dependencies() {
    final service = Get.find<AuthService>();
    final state = Get.put(FeatureSharedState(), permanent: true);

    HomeBindings().dependencies();
    VibesBindings().dependencies();
    ProfileBindings(isOwnProfile: true).dependencies();
    
    Get.lazyPut(() => AppNavigationController(service, state), fenix: true);
    Get.lazyPut(() => NotificationController(service, state), fenix: true);
    Get.lazyPut(() => ProfileSideMenuController(service, state), fenix: true);
  }
}
