import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/app_navigation/app_navigation_pages.dart';
import 'package:ivent_app/features/auth/auth_pages.dart';

class AuthMiddleware extends GetMiddleware {
  @override
  RouteSettings? redirect(String? route) {
    final authService = Get.find<AuthService>();

    // Check if user is logged in and token is not expired
    if (authService.isLoggedIn && !authService.isTokenExpired()) {
      return null;
    }

    // If token is expired, logout and redirect to onboarding
    if (authService.isLoggedIn && authService.isTokenExpired()) {
      authService.logout();
    }

    return const RouteSettings(name: AuthPages.onboarding);
  }
}

class NoAuthMiddleware extends GetMiddleware {
  @override
  RouteSettings? redirect(String? route) {
    return Get.find<AuthService>().isLoggedIn ? const RouteSettings(name: AppNavigationPages.appNavigation) : null;
  }
}
