import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';

class IaBottomPanel extends StatelessWidget {
  final Widget body;
  final bool showSlideIndicator;

  const IaBottomPanel({
    super.key,
    required this.body,
    this.showSlideIndicator = true,
  });

  @override
  Widget build(BuildContext context) {
    if (kDebugMode) {
      debugPrint('🔍 IaBottomPanel building with body: ${body.runtimeType}');
      debugPrint('🔍 IaBottomPanel showSlideIndicator: $showSlideIndicator');
    }

    return Expanded(
      child: Container(
        decoration: const BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(30),
            topRight: Radius.circular(30),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            showSlideIndicator ? _buildSlideIndicator() : const SizedBox(height: 28),
            Expanded(
              child: Container(
                decoration: const BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(30),
                    topRight: Radius.circular(30),
                  ),
                ),
                child: body,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Container _buildSlideIndicator() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: AppDimensions.padding12),
      width: 50,
      height: 4,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        color: AppColors.mediumGrey,
      ),
    );
  }
}
