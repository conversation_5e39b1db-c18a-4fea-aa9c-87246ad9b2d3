import 'dart:ui';

import 'package:flutter/material.dart';

/// Centralized color palette for the iVent app
///
/// Organized by semantic meaning rather than hex values
/// This makes it easier to maintain and update themes
class AppColors {
  AppColors._();

  // Primary brand colors
  static const Color primary = Color(0xFF01AEBE);
  static const Color primaryLight = Color(0xFF33C1CE);
  static const Color primaryDark = Color(0xFF008A96);

  // Secondary colors
  // static const Color secondary = Color(0xFFFF6B6B);
  // static const Color secondaryLight = Color(0xFFFF9999);
  // static const Color secondaryDark = Color(0xFFCC5555);
  static const Color secondary = Color(0xFFFF9800);
  static const Color secondaryLight = Color(0xFFFFC107);
  static const Color secondaryDark = Color(0xFFC68400);

  // Neutral colors
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  static const Color grey50 = Color(0xFFFAFAFA);
  static const Color grey100 = Color(0xFFF5F5F5);
  static const Color grey200 = Color(0xFFEEEEEE);
  static const Color grey300 = Color(0xFFE0E0E0);
  static const Color grey400 = Color(0xFFBDBDBD);
  static const Color grey500 = Color(0xFF9E9E9E);
  static const Color grey600 = Color(0xFF757575);
  static const Color grey700 = Color(0xFF616161);
  static const Color grey800 = Color(0xFF424242);
  static const Color grey900 = Color(0xFF212121);

  static const Color lightGrey = grey200;
  static const Color mediumGrey = grey400;
  static const Color darkGrey = grey600;
  static const Color veryDarkGrey = grey900;

  static const Color circleAvatarBackgrond = mediumGrey;

  // Semantic colors
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF6B6B);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);

  // Text colors
  static const Color textPrimary = veryDarkGrey;
  static const Color textSecondary = darkGrey;
  static const Color textTertiary = mediumGrey;
  static const Color textOnPrimary = white;
  static const Color textOnSecondary = white;

  // On-color variants
  static const Color onPrimary = white;
  static const Color onSecondary = white;

  // Background colors
  static const Color background = white;
  static const Color backgroundPrimary = white;
  static const Color backgroundSecondary = grey50;
  static const Color backgroundTertiary = grey100;

  // Surface colors
  static const Color surface = white;
  static const Color surfaceVariant = grey100;

  // Border colors
  static const Color border = grey300;
  static const Color borderLight = grey200;
  static const Color borderDark = grey400;

  // Shadow colors
  static const Color shadow = Color(0x1A000000);
  static const Color shadowLight = Color(0x0D000000);
  static const Color shadowDark = Color(0x33000000);

  // Transparent colors
  static const Color transparent = Colors.transparent;
  static const Color transparentBlack = Color(0x00000000);
  static const Color transparentWhite = Color(0x00FFFFFF);
  static const Color semiTransparentBlackZero = Color(0x77000000);
  static const Color semiTransparentBlack = Color(0xAA000000);
  static const Color semiTransparentWhite = Color(0x80FFFFFF);

  // Other colors
  static const Color purple = Color(0xFF7F3BB8);
  static const Color purpleLight = Color(0xFFE7E0FD);
  static const Color whatsappGreen = Color(0xFF00C307);
  static const Color instagram = Color(0xFFE2306C);
  static const Color heartRed = Color(0xFFE2306C);
  static const Color starYellow = Color(0xFFF4D839);
  static const Color orange = Color(0xFFFF9800);
  static const Color destructive = Color(0xFFEB3620);

  // Gradients
  static const Gradient gradientBlackL = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      AppColors.black,
      AppColors.transparentBlack,
    ],
  );

  static const Gradient gradientBlackS = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      AppColors.semiTransparentBlack,
      AppColors.transparentBlack,
    ],
  );

  static const Gradient gradientWhite = LinearGradient(
    begin: Alignment.bottomCenter,
    end: Alignment.topCenter,
    colors: [
      AppColors.white,
      AppColors.semiTransparentWhite,
      AppColors.transparentWhite,
    ],
  );

  static const Gradient gradientVibe = LinearGradient(
    colors: [
      semiTransparentBlack,
      transparentBlack,
      transparentBlack,
      black,
    ],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    stops: const [0.0, 0.2, 0.8, 1],
  );

  static const Gradient gradientVibeUpload = LinearGradient(
    colors: [
      semiTransparentBlackZero,
      transparentBlack,
      transparentBlack,
      black,
    ],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    stops: const [0.0, 0.1, 0.8, 1],
  );

  // Box shadows
  static const List<BoxShadow> usualShadow = [
    BoxShadow(
      color: shadow,
      blurRadius: 4,
      spreadRadius: 2,
    ),
  ];

  // Blurs
  static ImageFilter blur = ImageFilter.blur(sigmaX: 3.0, sigmaY: 3.0);

  // Color schemes for different themes
  static const ColorScheme lightColorScheme = ColorScheme.light(
    primary: primary,
    primaryContainer: primaryLight,
    secondary: secondary,
    secondaryContainer: secondaryLight,
    surface: surface,
    error: error,
    onPrimary: textOnPrimary,
    onSecondary: textOnSecondary,
    onSurface: textPrimary,
    onError: white,
  );

  static const ColorScheme darkColorScheme = ColorScheme.dark(
    primary: primaryLight,
    primaryContainer: primaryDark,
    secondary: secondaryLight,
    secondaryContainer: secondaryDark,
    surface: grey800,
    error: error,
    onPrimary: black,
    onSecondary: black,
    onSurface: white,
    onError: black,
  );
}
